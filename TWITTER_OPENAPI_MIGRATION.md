# Twitter OpenAPI TypeScript 迁移指南

## 概述

本项目已成功将推特数据获取从 Playwright 浏览器自动化方式迁移到 `twitter-openapi-typescript` 库，同时保留了原有的 Playwright 实现作为备用方案。

## 主要改动

### 1. 新增依赖和配置

- **新增文件**: `src/bot/helpers/twitter-config.ts` - Twitter API 配置和错误处理
- **新增文件**: `src/bot/helpers/twitter-test.ts` - 测试工具
- **修改文件**: `src/bot/helpers/twitter.ts` - 主要的 Twitter API 调用逻辑

### 2. 核心功能改造

#### `getTweetDetail(url: string)`
- **之前**: 使用 Playwright 浏览器拦截 API 响应
- **现在**: 优先使用 `twitter-openapi-typescript`，失败时回退到 Playwright
- **新增功能**: 
  - 重试机制（最多3次）
  - 性能监控和日志记录
  - Cookie 轮换
  - 错误分类和处理

#### `queryUser(screenName: string)`
- **之前**: 直接使用 fetch 调用 Twitter GraphQL API
- **现在**: 优先使用 `twitter-openapi-typescript`，失败时回退到原方法
- **改进**: 数据格式转换，保持与现有代码的兼容性

#### `queryUserTweets(userId: string, cursor?: string)`
- **之前**: 直接使用 fetch 调用 Twitter GraphQL API
- **现在**: 优先使用 `twitter-openapi-typescript`，失败时回退到原方法
- **改进**: 支持分页和游标

### 3. 新增特性

#### 错误处理和重试机制
```typescript
// 自动重试失败的请求
await withRetry(async () => {
  // API 调用
}, maxRetries, delay)
```

#### Cookie 轮换
- 自动轮换多个 Twitter 账号的 Cookie
- 登录失败时自动切换到下一个 Cookie

#### 性能监控
- 记录每次 API 调用的耗时
- 成功/失败状态跟踪
- 详细的日志记录

#### 回退策略
- `twitter-openapi-typescript` 失败时自动回退到 Playwright
- 可通过配置禁用回退策略

## 配置选项

在 `src/bot/helpers/twitter-config.ts` 中可以调整以下配置：

```typescript
export const TWITTER_CONFIG = {
  maxRetries: 3,           // 最大重试次数
  retryDelay: 1000,        // 重试延迟（毫秒）
  timeout: 30000,          // 请求超时（毫秒）
  cookieRotationEnabled: true,     // 启用 Cookie 轮换
  fallbackToPlaywright: true,      // 启用 Playwright 回退
}
```

## 使用方法

### 基本使用
```typescript
import { getTweetDetail, queryUser, queryUserTweets } from '#root/bot/helpers/twitter.js'

// 获取推特详情
const tweetDetail = await getTweetDetail('https://x.com/user/status/123456789')

// 获取用户信息
const userInfo = await queryUser('elonmusk')

// 获取用户推文
const userTweets = await queryUserTweets('44196397')
```

### 测试
```bash
# 运行测试
tsx src/bot/helpers/twitter-test.ts
```

## 数据结构兼容性

改造后的 API 返回的数据结构与原有格式完全兼容，现有的业务逻辑无需修改。

### TweetDetail 数据结构
```typescript
{
  tombstone: null | object,     // 推文是否被删除或私有
  legacy: object,               // 推文基本信息
  noteTweetFullText: string,    // 长推文内容
  media: array,                 // 媒体文件信息
  user: object,                 // 用户信息
}
```

## 优势

1. **更稳定**: 减少了浏览器自动化的不稳定性
2. **更快速**: 直接 API 调用比浏览器自动化更快
3. **更可靠**: 内置重试机制和错误处理
4. **向后兼容**: 保留 Playwright 作为备用方案
5. **易于维护**: 清晰的错误分类和日志记录

## 注意事项

1. **Cookie 管理**: 需要定期更新 `cookieList` 中的 Cookie 数据
2. **速率限制**: Twitter API 有速率限制，建议合理控制请求频率
3. **错误监控**: 建议监控日志中的错误信息，及时处理异常情况

## 故障排除

### 常见问题

1. **登录失败**: 检查 Cookie 是否过期，更新 `cookieList`
2. **API 限流**: 等待一段时间后重试，或增加请求间隔
3. **数据格式错误**: 检查 `formatTweetDetailFromOpenApi` 函数的数据转换逻辑

### 日志分析
- 查看 `Twitter API 登录成功` 日志确认登录状态
- 查看 `Twitter API 调用成功/失败` 日志分析性能和错误
- 查看 `回退到 Playwright 方式` 日志了解回退情况

## 未来改进

1. 支持更多的 Twitter API 端点
2. 实现更智能的 Cookie 管理
3. 添加缓存机制减少 API 调用
4. 支持批量操作优化性能
