#!/usr/bin/env tsx

import process from 'node:process'
import { type <PERSON><PERSON><PERSON><PERSON>, run } from '@grammyjs/runner'
import { logger } from '#root/logger.js'
import type { Bot } from '#root/bot/index.js'
import { createBot } from '#root/bot/index.js'
import type { PollingConfig, WebhookConfig } from '#root/config.js'
import { config } from '#root/config.js'
import { createServer, createServerManager } from '#root/server/index.js'
import { shutdownRedis } from '#root/redis/shutdown.js'
// 导入 broadcastWorker 以确保它被初始化
import '#root/redis/boardcast.js'

// eslint-disable-next-line import/no-mutable-exports
export let _botClient: Bot | undefined
async function startPolling(config: PollingConfig) {
  const bot = createBot(config.botToken, {
    config,
    logger,
  })
  _botClient = bot
  const server = createServer({
    bot,
    config,
    logger,
  })
  const serverManager = createServerManager(server, {
    host: config.serverHost,
    port: config.serverPort,
  })
  let runner: undefined | RunnerHandle

  // graceful shutdown
  onShutdown(async () => {
    logger.info('Shutdown')
    await runner?.stop()
    await shutdownRedis()
    await serverManager.stop()
  })

  await Promise.all([
    bot.init(),
    bot.api.deleteWebhook({
      drop_pending_updates: true,
    }),
  ])

  // start bot
  runner = run(bot, {
    runner: {
      fetch: {
        allowed_updates: config.botAllowedUpdates,
      },
    },
  })

  logger.info({
    msg: '🤖 Bot running...',
    username: bot.botInfo.username,
  })

  // start server
  const info = await serverManager.start()
  logger.info({
    msg: '🔥 Server started',
    url: info.url,
  })
}

async function startWebhook(config: WebhookConfig) {
  const bot = createBot(config.botToken, {
    config,
    logger,
  })
  _botClient = bot
  const server = createServer({
    bot,
    config,
    logger,
  })
  const serverManager = createServerManager(server, {
    host: config.serverHost,
    port: config.serverPort,
  })

  // graceful shutdown
  onShutdown(async () => {
    logger.info('Shutdown')
    await shutdownRedis()
    await serverManager.stop()
  })

  // to prevent receiving updates before the bot is ready
  await bot.init()

  // start server
  const info = await serverManager.start()
  logger.info({
    msg: '🔥 Server started',
    url: info.url,
  })

  // set webhook
  await bot.api.setWebhook(config.botWebhook, {
    allowed_updates: config.botAllowedUpdates,
    secret_token: config.botWebhookSecret,
    drop_pending_updates: true,
  })
  logger.info({
    msg: '🤖 Webhook was set',
    url: config.botWebhook,
  })
}

try {
  if (config.isWebhookMode)
    await startWebhook(config)
  else if (config.isPollingMode)
    await startPolling(config)
}
catch (error) {
  logger.error(error)
  process.exit(1)
}

// Utils

function onShutdown(cleanUp: () => Promise<void>) {
  let isShuttingDown = false
  const handleShutdown = async () => {
    if (isShuttingDown)
      return
    isShuttingDown = true
    await cleanUp()
  }
  process.on('SIGINT', handleShutdown)
  process.on('SIGTERM', handleShutdown)
}
