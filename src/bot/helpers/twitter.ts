import { filter, find, get, isEmpty, map, omit } from 'lodash-es'
import { chromium } from 'playwright'
import { TwitterOpenApi } from 'twitter-openapi-typescript'
import { TWITTER_CONFIG, TwitterApiError, logTwitterApiCall, withRetry } from './twitter-config.js'
import { logger } from '#root/logger.js'

const browser = await chromium.launch({
  headless: true,
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--window-size=1920,1080',
    '--disable-blink-features=AutomationControlled',
    '--disable-web-security',
    '--disable-features=IsolateOrigins,site-per-process',
    // 添加更多反检测参数
    '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  ],
})
const browserContext = await browser.newContext({
  viewport: { width: 1920, height: 1080 },
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  // 模拟设备特性
  deviceScaleFactor: 2,
  hasTouch: false,
  isMobile: false,
  // 添加语言和时区设置
  locale: 'en-US',
  timezoneId: 'America/New_York',
})

export type XUser = {
  user_result_by_screen_name: {
    result: {
      __typename: string
      legacy: {
        blocking: boolean
        blocked_by: boolean
        protected: boolean
        following: boolean
        followed_by: boolean
        name: string
        screen_name: string
      }
      rest_id: string
      profilemodules: {
        v1: []
      }
      id: string
    }
    id: string
  }
} | Record<string, never>

export async function validateTwitter(twitterUrl: string) {
  const url = twitterUrl.split('?')[0]
  // ['https:', '', 'twitter.com', 'guang56835']
  const urlArray = url.split('/')
  const length = urlArray.length
  const urlName = urlArray[2]
  const isTwitterUrl = (urlName === 'twitter.com' || urlName === 'x.com') && length > 3
  const isTweet = urlArray[4] === 'status' && length === 6
  const screenName = urlArray[3]
  const isTweeterUser = screenName && length === 4
  return {
    isTwitterUrl,
    isTweet,
    isTweeterUser,
    screenName,
  }
}

// 将对象转换为查询字符串
function toQueryString(params: Record<string, any>): string {
  const queryString = Object.keys(params)
    .map((key) => {
      const value = params[key]
      if (typeof value === 'object') {
        return `${key}=${encodeURIComponent(JSON.stringify(value))}`
      }
      return `${key}=${value}`
    })
    .join('&')

  return queryString
}
// 获取推文ID
export async function getTweetId(twitterUrl: string): Promise<string> {
  const strArr = twitterUrl.split('/')
  const urlIdx = strArr.findIndex(str => str.includes('twitter.com') || str.includes('x.com'))
  if (urlIdx === -1 || strArr[urlIdx + 2] !== 'status') {
    throw new Error('Please enter the correct Twitter URL')
  }

  const idIndex = urlIdx + 3
  const id = strArr[idIndex]?.split('?')[0]
  return id
}
function getLegacy(tweetResult: any) {
  if (tweetResult.__typename === 'TweetWithVisibilityResults') {
    return get(tweetResult, ['tweet', 'legacy', 'retweeted_status_result', 'result', 'tweet', 'legacy'])
      || get(tweetResult, ['tweet', 'legacy'])
  }
  return get(tweetResult, ['legacy', 'retweeted_status_result', 'result', 'legacy'])
    || get(tweetResult, ['legacy'])
}

/**
 * @param userEntries 批量下载用到的参数
 */
function getTweetResult(data: any, id: string, userEntries?: any[]) {
  const basePath = ['threaded_conversation_with_injections_v2', 'instructions']
  const instructions = get(data, basePath)
  const tweetResultsPath = ['content', 'itemContent', 'tweet_results', 'result']

  const entries = userEntries || find(instructions, { type: 'TimelineAddEntries' })?.entries
  const targetTweet = find(entries, item => item.entryId.includes(id))
  const tweetResult = get(targetTweet, tweetResultsPath)
  const user = get(tweetResult, ['core', 'user_results', 'result', 'legacy'])
  // 先判断是否是转发的tweet
  const legacy = getLegacy(tweetResult)
  // type: photo | video  判断是视频还是图片
  // media_url_https 封面  sensitive_media_warning.adult_content 是否敏感信息
  // 是 video 取 .video_info | duration_millis 时长 variants 下载列表
  let media = get(legacy, ['entities', 'media'], [])
  if (!media.length) {
    const tweetWithVisibilityPath = ['tweet', 'card', 'legacy', 'binding_values']
    const tweetPath = ['card', 'legacy', 'binding_values']
    const cardValues = get(tweetResult, tweetWithVisibilityPath) || get(tweetResult, tweetPath)
    const unifiedCard = find(cardValues, { key: 'unified_card' })
    if (unifiedCard?.value?.string_value) {
      try {
        const unifiedCardJson = JSON.parse(unifiedCard.value.string_value)
        const cardMedia = Object.values(unifiedCardJson.media_entities)
        if (cardMedia?.length) {
          media = cardMedia
        }
      }
      catch (err: any) {
        logger.info({
          message: `unified_card parse error: ${err.message}`,
        })
      }
    }
  }
  const noteTweetFullText = get(tweetResult, ['note_tweet', 'note_tweet_results', 'result', 'text'])
  return {
    tombstone: tweetResult?.tombstone,
    legacy: omit(legacy, ['extended_entities']),
    noteTweetFullText,
    media,
    user,
  }
}
// 获取推文详情
// export async function getTweetDetail(url: string, option: any = {}) {
//   const { showTread = false, cursor } = option
//   const id = await getTweetId(url)
//   // finch4AI账号
//   const headers = {
//     'accept': '*/*',
//     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
//     'authorization': 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA',
//     'cache-control': 'no-cache',
//     'content-type': 'application/json',
//     'pragma': 'no-cache',
//     'priority': 'u=1, i',
//     'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
//     'sec-ch-ua-mobile': '?0',
//     'sec-ch-ua-platform': '"macOS"',
//     'sec-fetch-dest': 'empty',
//     'sec-fetch-mode': 'cors',
//     'sec-fetch-site': 'same-origin',
//     'x-client-transaction-id': 'tEc+Br9vta5zUgYNPZYGsecjnNl02SWvv3CYh7uxjCAPffXJsvbzn01xtIZcdobb9nT+Cbd5GgH4fcwkvbS/jjM2llHMtw',
//     'x-client-uuid': 'da8fbcf5-4cf6-4777-a7f2-a237ae86b982',
//     'x-csrf-token': '04ef79942e760ad1a932f7ce0495cfd062b68cabe55d10b635905b8dcd32f3bdf510433f027193fa5de31b46a9b274b963ef21a89e9a57d509ff1fe01de5915ce99ad3a3b86ed5b75cf696ddd32dbcd5',
//     'x-twitter-active-user': 'yes',
//     'x-twitter-auth-type': 'OAuth2Session',
//     'x-twitter-client-language': 'en',
//     'cookie': 'lang=en; dnt=1; kdt=2hDzxPVtDBS1JFZe7PyUe6ZdbJy6kEgulgcba128; twtr_pixel_opt_in=Y; g_state={\"i_l\":0}; des_opt_in=Y; personalization_id=\"v1_DOsfU6OQyc4A6iNtoOfUHA==\"; _ga_RJGMY4G45L=GS1.1.1744113525.5.0.1744113534.51.0.0; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%220195a810-3eea-7ea1-b678-c9f1d550f945%22%2C%22%24sesid%22%3A%5B1744113568870%2C%2201961544-8d84-7ee2-a9bf-08be155846da%22%2C1744113536388%5D%7D; _ga=GA1.2.984266070.1742140038; __cf_bm=oDXcC_VdS9vhpH.ALfmUyW.2_auAWkRbaNv9Z_KdLbQ-1745661266-*******-QDQjHJPXzUkybdqQ86KFyeRhBFq6abgOpVPDJJUx_hQY9Fv3Tl1EPL6_cCc.2ddVwxMitrq7Wr2tF9hI6C7MZbVfpbOJIMRB.KoDIzCGPEw; ads_prefs=\"HBESAAA=\"; auth_multi=\"1732950713716948992:c9398eadb062be5c026f1beeeea77a9d11e0e1ec|1801233935235514368:df0add03c7af261021876342686ac5c0e2ec902c|1801234315579179008:4afa1ab9dab1befe6a605c11f3572333bd0978b6\"; auth_token=8d22b3ebedc2fbe55dfa70f040750d143305d98a; guest_id_ads=v1%3A174566144951438191; guest_id_marketing=v1%3A174566144951438191; guest_id=v1%3A174566144951438191; twid=u%3D1700415026576891904; ct0=04ef79942e760ad1a932f7ce0495cfd062b68cabe55d10b635905b8dcd32f3bdf510433f027193fa5de31b46a9b274b963ef21a89e9a57d509ff1fe01de5915ce99ad3a3b86ed5b75cf696ddd32dbcd5',
//     'Referrer-Policy': 'strict-origin-when-cross-origin',
//   }

//   const queryParams: Record<string, any> = {
//     variables: {
//       focalTweetId: id, // 使用变量id替换原值
//       referrer: 'tweet', // 修改为tweet
//       controller_data: 'DAACDAABDAABCgABBAAAQkICAAEKAAIAAEAAEAAgAAoACQApidROexZ5CAALAAAAAw8ADAMAAAAgAQACQkIAAAQAIAAQAEAAAAgAAAAAAAAAgAAgACAA4AEKAA6nlB7xv2lFgAoAELUj+i6AGEEIAAAAAA==',
//       with_rux_injections: false,
//       rankingMode: 'Relevance',
//       includePromotedContent: true,
//       withCommunity: true,
//       withQuickPromoteEligibilityTweetFields: true,
//       withBirdwatchNotes: true,
//       withVoice: true,
//     },
//     features: {
//       rweb_video_screen_enabled: false,
//       profile_label_improvements_pcf_label_in_post_enabled: true,
//       rweb_tipjar_consumption_enabled: true,
//       verified_phone_label_enabled: false,
//       creator_subscriptions_tweet_preview_api_enabled: true,
//       responsive_web_graphql_timeline_navigation_enabled: true,
//       responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
//       premium_content_api_read_enabled: false,
//       communities_web_enable_tweet_community_results_fetch: true,
//       c9s_tweet_anatomy_moderator_badge_enabled: true,
//       responsive_web_grok_analyze_button_fetch_trends_enabled: false,
//       responsive_web_grok_analyze_post_followups_enabled: true,
//       responsive_web_jetfuel_frame: false,
//       responsive_web_grok_share_attachment_enabled: true,
//       articles_preview_enabled: true,
//       responsive_web_edit_tweet_api_enabled: true,
//       graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
//       view_counts_everywhere_api_enabled: true,
//       longform_notetweets_consumption_enabled: true,
//       responsive_web_twitter_article_tweet_consumption_enabled: true,
//       tweet_awards_web_tipping_enabled: false,
//       responsive_web_grok_show_grok_translated_post: false,
//       responsive_web_grok_analysis_button_from_backend: true,
//       creator_subscriptions_quote_tweet_preview_enabled: false,
//       freedom_of_speech_not_reach_fetch_enabled: true,
//       standardized_nudges_misinfo: true,
//       tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
//       longform_notetweets_rich_text_read_enabled: true,
//       longform_notetweets_inline_media_enabled: true,
//       responsive_web_grok_image_annotation_enabled: true,
//       responsive_web_enhance_cards_enabled: false,
//     },
//     fieldToggles: {
//       withArticleRichContentState: true,
//       withArticlePlainText: false,
//       withGrokAnalyze: false,
//       withDisallowedReplyControls: false,
//     },
//   }
//   if (cursor) {
//     queryParams.variables.cursor = cursor
//     queryParams.variables.referrer = 'tweet'
//   }
//   // 构建查询参数字符串
//   const queryString = toQueryString(queryParams)
//   const tweetDetailApi = `https://twitter.com/i/api/graphql/_8aYOgEDz35BrBcBal1-_w/TweetDetail?${queryString}`
//   const result = await fetch(tweetDetailApi, {
//     method: 'GET',
//     headers,
//   })
//   if (!result.ok) {
//     throw new Error(`Hey, we've run into some problems. We're working on it right now. Hang tight!\n我们遇到一些技术问题 目前正在修复 请耐心等待`)
//   }
//   const { data } = await result.json()
//   if (showTread)
//     return data
//   if (isEmpty(data)) {
//     throw new Error('Video Not Found: This tweet may have been deleted. Please confirm whether the tweet still exists.')
//   }
//   const fmtData = getTweetResult(data, id)
//   if (fmtData.tombstone) {
//     throw new Error('Unable to view, this tweet is private or blocked.')
//   }
//   return fmtData
// }

function convertTwitterCookies(rawCookies: any[]) {
  return rawCookies.map(cookie => ({
    name: cookie.name,
    value: decodeURIComponent(cookie.value), // 解码 URL 编码值
    domain: cookie.domain.replace(/^\./, ''), // 移除域名前导点
    path: cookie.path,
    expires: Math.floor(cookie.expirationDate), // 转换为整数时间戳
    httpOnly: cookie.httpOnly,
    secure: cookie.secure,
    sameSite: cookie.sameSite === 'no_restriction'
      ? 'None'
      : cookie.sameSite === 'lax'
        ? 'Lax'
        : 'Lax', // 默认值
    // 以下可选字段根据 Puppeteer 版本处理
    ...(cookie.priority && { priority: cookie.priority }),
    ...(cookie.sameParty && { sameParty: cookie.sameParty }),
  })).filter(c =>
    c.name && c.value
    && c.expires > Math.floor(Date.now() / 1000), // 过滤过期 Cookie
  )
}
let cookieIdx = 0
const cookieList = [
  // soa
  [
    {
      domain: '.x.com',
      expirationDate: 1787338222.570208,
      hostOnly: false,
      httpOnly: true,
      name: 'auth_token',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '28d6cc4fc1716de404b91eaf58df1b4e92ad9c3d',
    },
    {
      domain: '.x.com',
      expirationDate: 1752787175.919906,
      hostOnly: false,
      httpOnly: false,
      name: 'gt',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: '1945918823854682530',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338175.795271,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175277817566819729',
    },
    {
      domain: '.x.com',
      expirationDate: 1784314224.568174,
      hostOnly: false,
      httpOnly: false,
      name: 'twid',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'u%3D168574344',
    },
    {
      domain: '.x.com',
      expirationDate: 1752779975.795292,
      hostOnly: false,
      httpOnly: true,
      name: '__cf_bm',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'tRUJ8Ul_SN3Q9BhQVjM5AtrKZ6DjthsUWVG.fJqW3iE-1752778175-*******-eZItRttFpaPopnix3gK0gLa0zfL3XkTbd8xsvzz36CKcMrEqsIcfa64EXSkumfaNS3rcpkgxhx2g8HJP5YlPBdesZxOrhi8onhtPWpTmhZQ',
    },
    {
      domain: '.x.com',
      expirationDate: 1752864623.333377,
      hostOnly: false,
      httpOnly: true,
      name: 'att',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '1-JVzqasi5amUKEoFqpV4p2OVEyMcvDjAT5Dw9XUm1',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338223.006142,
      hostOnly: false,
      httpOnly: false,
      name: 'ct0',
      path: '/',
      sameSite: 'lax',
      secure: true,
      session: false,
      storeId: null,
      value: '3ea620035648995fc812c0635580d828a7e5f5962f66127818d3c94dadeb98096ce6f0c4f7eb1a3aad8bcda75d3920663a69d1046fcbeb13b1c58ee17327541d832ea34b38d90170d203b90ade661b02',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338224.568119,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_ads',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175277817566819729',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338224.568158,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_marketing',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175277817566819729',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338222.569952,
      hostOnly: false,
      httpOnly: true,
      name: 'kdt',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'o2ynYqZWvl7LiCRZSAQI7HlgpW1ShfZXdMv8yt3r',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338175.919706,
      hostOnly: false,
      httpOnly: false,
      name: 'personalization_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '"v1_RfSFByDYvB8+9Nw/di1uWg=="',
    },
  ],
  // fin
  [
    {
      domain: '.x.com',
      expirationDate: 1786465523.417498,
      hostOnly: false,
      httpOnly: true,
      name: 'auth_token',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '0ebb7fbf890dd14727b8a1efec76018ba8917c62',
    },
    {
      domain: '.x.com',
      expirationDate: 1786465508.208717,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175190550828297838',
    },
    {
      domain: '.x.com',
      expirationDate: 1784314361.265191,
      hostOnly: false,
      httpOnly: false,
      name: 'twid',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'u%3D1732950713716948992',
    },
    {
      domain: '.x.com',
      hostOnly: false,
      httpOnly: true,
      name: '_twitter_sess',
      path: '/',
      sameSite: null,
      secure: true,
      session: true,
      storeId: null,
      value: 'BAh7BiIKZmxhc2hJQzonQWN0aW9uQ29udHJvbGxlcjo6Rmxhc2g6OkZsYXNo%250ASGFzaHsABjoKQHVzZWR7AA%253D%253D--1164b91ac812d853b877e93ddb612b7471bebc74',
    },
    {
      domain: '.x.com',
      expirationDate: 1753355649.913655,
      hostOnly: false,
      httpOnly: false,
      name: 'external_referer',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'padhuUp37zjKE2wiJkYN0NRYfw9W8LjYtlMH7DtAmxU%3D|0|8e8t2xd8A2w%3D',
    },
    {
      domain: '.x.com',
      expirationDate: 1752780154.314092,
      hostOnly: false,
      httpOnly: true,
      name: '__cf_bm',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '_9wiMpSJkazhw7m8.VDrdfosY9O6Px7EXpI4WjFGU7M-1752778354-*******-HItu_oT4zmQw3ZVRymg9Dyohgq2Qnzed8d0hCXS4Fl2pRp2.CJGm6Kb_ZWQGH1sbJTDuqzAzKbfvGmwgDfx7eLuqmiSEPu_fe_NHoRrDAoY',
    },
    {
      domain: '.x.com',
      expirationDate: 1786465524.118839,
      hostOnly: false,
      httpOnly: false,
      name: 'ct0',
      path: '/',
      sameSite: 'lax',
      secure: true,
      session: false,
      storeId: null,
      value: 'b226aa8ebc241ce76f54cd9bae0776b29375399122fbff8d4e3306091176657803e68a59bea40913a6a73a9e5584f85283af0d4e7009684671dfd55835c6181cff15851cf2e3a3ab140f0a3468818234',
    },
    {
      domain: '.x.com',
      expirationDate: 1767780446.459618,
      hostOnly: false,
      httpOnly: false,
      name: 'd_prefs',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'MToxLGNvbnNlbnRfdmVyc2lvbjoyLHRleHRfdmVyc2lvbjoxMDAw',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338361.265139,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_ads',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175190550828297838',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338361.265174,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_marketing',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175190550828297838',
    },
    {
      domain: '.x.com',
      expirationDate: 1786465523.417328,
      hostOnly: false,
      httpOnly: true,
      name: 'kdt',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'n4iwVUYKQqc79rsRwsH5I5D49eZlJIOEXNpdLunW',
    },
    {
      domain: '.x.com',
      expirationDate: 1786442848.477498,
      hostOnly: false,
      httpOnly: false,
      name: 'personalization_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '"v1_L/p905yLwqyLP0PdvvC+tQ=="',
    },
  ],
  // chatAi
  [
    {
      domain: '.x.com',
      expirationDate: 1787338468.164999,
      hostOnly: false,
      httpOnly: true,
      name: 'auth_token',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '2a4afae3d10ad2c68cf5213821d10bfa5d5e0497',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338468.436349,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175277846829019435',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338468.164822,
      hostOnly: false,
      httpOnly: false,
      name: 'ads_prefs',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '"HBISAAA="',
    },
    {
      domain: '.x.com',
      expirationDate: 1784314477.645575,
      hostOnly: false,
      httpOnly: false,
      name: 'twid',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'u%3D1801234315579179008',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338469.260772,
      hostOnly: false,
      httpOnly: true,
      name: 'auth_multi',
      path: '/',
      sameSite: 'lax',
      secure: true,
      session: false,
      storeId: null,
      value: '"1700415026576891904:9aca926e8744692fd32fe861103d765ee15cfb92"',
    },
    {
      domain: '.x.com',
      expirationDate: 1752780264.96822,
      hostOnly: false,
      httpOnly: true,
      name: '__cf_bm',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '7pmvPaZBJokyORAOA7eyRlHLBOabKTYHWHOcsR0oyek-1752778464-*******-g.jhZFXulAceczPoXRPe1OXmu6KzyAHHQM1hbADExFNZ34YO.P8Q4EocBw9uB9ZWA6dSzSrmkCBc1R_xN_FYRBgsGIZ_4n8_utFktFj1CTM',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338468.436446,
      hostOnly: false,
      httpOnly: false,
      name: 'ct0',
      path: '/',
      sameSite: 'lax',
      secure: true,
      session: false,
      storeId: null,
      value: 'ce83bd4c7a4ba2b28b318a04b460be2ce502bb5826032e4df515632f8bd6427ee9fc7e225556d3ebad9c9369eaed085f5a631835c724105ec2c2415035a908452d0982be1b918b71557315cbb539fdf9',
    },
    {
      domain: '.x.com',
      expirationDate: 1767781481.54511,
      hostOnly: false,
      httpOnly: false,
      name: 'd_prefs',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'MToxLGNvbnNlbnRfdmVyc2lvbjoyLHRleHRfdmVyc2lvbjoxMDAw',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338468.164764,
      hostOnly: false,
      httpOnly: false,
      name: 'dnt',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '1',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338477.645499,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_ads',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175277846829019435',
    },
    {
      domain: '.x.com',
      expirationDate: 1787338477.645549,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_marketing',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175277846829019435',
    },
    {
      domain: '.x.com',
      expirationDate: 1786616548.605635,
      hostOnly: false,
      httpOnly: true,
      name: 'kdt',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'wmCaLlk5x63Nn2JbyL5v3XNYylwjG0zgegOojDXA',
    },
    {
      domain: '.x.com',
      expirationDate: 1786443882.869493,
      hostOnly: false,
      httpOnly: false,
      name: 'personalization_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '"v1_TREijUWuYXK3l1FdTxR4wg=="',
    },
  ],
]

// 创建 TwitterOpenApi 实例
let twitterApi: TwitterOpenApi | null = null

async function getTwitterApi() {
  if (!twitterApi) {
    twitterApi = new TwitterOpenApi()

    // 使用重试机制进行登录
    await withRetry(async () => {
      const cookies = convertTwitterCookies(cookieList[cookieIdx % cookieList.length])
      const cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ')

      try {
        await twitterApi!.loginWithCookies(cookieString)
        logger.info(`Twitter API 登录成功，使用 cookie 索引: ${cookieIdx}`)
      }
      catch (error: any) {
        logger.error(`Twitter API 登录失败，cookie 索引 ${cookieIdx}: ${error.message}`)

        // 轮换到下一个 cookie
        if (TWITTER_CONFIG.cookieRotationEnabled) {
          cookieIdx = (cookieIdx + 1) % cookieList.length
        }

        throw new TwitterApiError(
          `Twitter API 登录失败: ${error.message}`,
          'LOGIN_FAILED',
          error.statusCode,
          error,
        )
      }
    })
  }
  return twitterApi
}

export async function getTweetDetail(url: string) {
  const startTime = Date.now()
  let success = false

  try {
    const id = await getTweetId(url)

    // 使用重试机制获取推特详情
    const fmtData = await withRetry(async () => {
      const api = await getTwitterApi()

      // 使用 twitter-openapi-typescript 获取推特详情
      const tweetResult = await api.getTweetDetail(id)

      if (!tweetResult || isEmpty(tweetResult)) {
        throw new TwitterApiError(
          'Video Not Found: This tweet may have been deleted. Please confirm whether the tweet still exists.',
          'TWEET_NOT_FOUND',
        )
      }

      // 转换数据格式以匹配现有的数据结构
      const formatted = formatTweetDetailFromOpenApi(tweetResult, id)

      if (formatted.tombstone) {
        throw new TwitterApiError(
          'Unable to view, this tweet is private or blocked.',
          'TWEET_PRIVATE_OR_BLOCKED',
        )
      }

      return formatted
    })

    success = true
    logTwitterApiCall('getTweetDetail', { url, id }, Date.now() - startTime, true)
    return fmtData
  }
  catch (error: any) {
    logTwitterApiCall('getTweetDetail', { url }, Date.now() - startTime, false)
    logger.error(`获取推文详情错误: ${error?.message}`)

    // 如果 twitter-openapi-typescript 失败且启用了回退策略，使用 Playwright 方式
    if (TWITTER_CONFIG.fallbackToPlaywright) {
      logger.info('回退到 Playwright 方式获取推特详情')
      return await getTweetDetailWithPlaywright(url)
    }

    throw error
  }
}

// 保留原有的 Playwright 实现作为备用方案
async function getTweetDetailWithPlaywright(url: string) {
  const page = await browserContext.newPage()
  try {
    const id = await getTweetId(url)
    const cookies = convertTwitterCookies(cookieList[cookieIdx++ % cookieList.length])
    await browserContext.addCookies(cookies)
    await page.goto(url, {
      timeout: 30000,
    })
    // 拦截API响应
    const apiResponse = await page.waitForResponse(res =>
      res.url().includes('/TweetDetail')
      && res.request().method() === 'GET',
    )
    const json = await apiResponse.json()

    if (isEmpty(json?.data)) {
      throw new Error('Video Not Found: This tweet may have been deleted. Please confirm whether the tweet still exists.')
    }
    const fmtData = getTweetResult(json.data, id)
    if (fmtData.tombstone) {
      throw new Error('Unable to view, this tweet is private or blocked.')
    }
    return fmtData
  }
  catch (error: any) {
    console.error('[ 获取推文详情错误 ] >', error?.message)
    throw new Error(error?.message ?? 'Something went wrong. Please try again later.')
  }
  finally {
    await page?.close()
  }
}

// 将 twitter-openapi-typescript 的数据格式转换为现有格式
function formatTweetDetailFromOpenApi(tweetResult: any, id: string) {
  try {
    // 从 twitter-openapi-typescript 的响应中提取数据
    const tweet = tweetResult?.data?.tweetResult?.result || tweetResult?.result || tweetResult

    if (!tweet) {
      throw new Error('Invalid tweet data structure')
    }

    // 检查是否是墓碑状态（已删除或私有）
    if (tweet.tombstone || tweet.__typename === 'TweetTombstone') {
      return { tombstone: tweet.tombstone || { text: { text: 'This tweet is unavailable' } } }
    }

    // 处理转发的推文
    let legacy = tweet.legacy
    if (tweet.__typename === 'TweetWithVisibilityResults') {
      legacy = tweet.tweet?.legacy || legacy
    }

    // 如果是转发，获取原始推文数据
    if (legacy?.retweeted_status_result) {
      legacy = legacy.retweeted_status_result.result?.legacy || legacy
    }

    // 获取媒体信息
    let media = legacy?.entities?.media || []

    // 如果没有媒体，尝试从 extended_entities 获取
    if (!media.length && legacy?.extended_entities?.media) {
      media = legacy.extended_entities.media
    }

    // 如果还是没有媒体，尝试从 card 获取
    if (!media.length && tweet.card?.legacy?.binding_values) {
      const unifiedCard = tweet.card.legacy.binding_values.find((item: any) => item.key === 'unified_card')
      if (unifiedCard?.value?.string_value) {
        try {
          const unifiedCardJson = JSON.parse(unifiedCard.value.string_value)
          const cardMedia = Object.values(unifiedCardJson.media_entities || {})
          if (cardMedia.length) {
            media = cardMedia
          }
        }
        catch (err: any) {
          logger.info(`unified_card parse error: ${err.message}`)
        }
      }
    }

    // 获取用户信息
    const user = tweet.core?.user_results?.result?.legacy || tweet.user?.legacy

    // 获取长推文内容
    const noteTweetFullText = tweet.note_tweet?.note_tweet_results?.result?.text

    return {
      tombstone: null,
      legacy: omit(legacy, ['extended_entities']),
      noteTweetFullText,
      media,
      user,
    }
  }
  catch (error: any) {
    logger.error(`格式化推文数据错误: ${error.message}`)
    throw new Error('Failed to format tweet data')
  }
}

// 用于批量请求的header 这样不会影响基本功能
const userHeaders = {
  'accept': '*/*',
  'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'authorization': 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA',
  'cache-control': 'no-cache',
  'content-type': 'application/json',
  'pragma': 'no-cache',
  'priority': 'u=1, i',
  'sec-ch-ua': '"Chromium";v="124", "Google Chrome";v="124", "Not-A.Brand";v="99"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'x-client-transaction-id': 'OfzTxzAvAIB6nUEWov1bptxTqwJ9DZh9FtEV3z9ix+orQxpOZiCZisLkGglHDh8OynHAPDv4AQjTu87V+ku08oMTL3N3Og',
  'x-client-uuid': 'fb3774b2-bebf-43c9-92b2-1485d254435b',
  'x-csrf-token': 'a0755d6476684d72f38469eb0e9c649d0ca23c084b01d1e6ccff1117068d74e805170c42ebcd0b127868a38b4b2a3485c45daf415c7a2b480ade725af71f755a2d4c1919a16fe597c4035326ab550138',
  'x-twitter-active-user': 'yes',
  'x-twitter-auth-type': 'OAuth2Session',
  'x-twitter-client-language': 'en',
  'cookie': 'g_state={"i_l":0}; lang=en; kdt=gHO92BQW783uObA820a8hYoiLr1OuS4Sxx1IKjox; dnt=1; des_opt_in=N; _ga=GA1.2.1550707208.1710250906; _gid=GA1.2.25848989.1716804751; auth_multi="1732950713716948992:6cce95383e8e08781ebebe9694ff7233418e0b32"; auth_token=8d22b3ebedc2fbe55dfa70f040750d143305d98a; guest_id=v1%3A171680478145608291; twid=u%3D1700415026576891904; ct0=a0755d6476684d72f38469eb0e9c649d0ca23c084b01d1e6ccff1117068d74e805170c42ebcd0b127868a38b4b2a3485c45daf415c7a2b480ade725af71f755a2d4c1919a16fe597c4035326ab550138; guest_id_ads=v1%3A171680478145608291; guest_id_marketing=v1%3A171680478145608291; personalization_id="v1_CUdFfS8zoJc5chi2nQ3T2w=="',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
}

export async function queryUser(screen_name: string): Promise<XUser> {
  try {
    const api = await getTwitterApi()
    const userResult = await api.getUserByScreenName(screen_name)

    if (userResult?.data?.user) {
      // 转换为现有格式
      return {
        user_result_by_screen_name: {
          result: {
            __typename: userResult.data.user.__typename || 'User',
            legacy: userResult.data.user.legacy,
            rest_id: userResult.data.user.rest_id,
            profilemodules: { v1: [] },
            id: userResult.data.user.rest_id,
          },
          id: userResult.data.user.rest_id,
        },
      }
    }

    return {}
  }
  catch (error: any) {
    logger.error(`获取用户信息失败，回退到原始方法: ${error.message}`)

    // 回退到原始的 fetch 方法
    const queryParams = {
      variables: { screen_name },
    }
    const queryString = toQueryString(queryParams)
    const userApi = `https://twitter.com/i/api/graphql/-0XdHI-mrHWBQd8-oLo1aA/ProfileSpotlightsQuery?${queryString}`
    const res = await fetch(userApi, {
      method: 'GET',
      headers: userHeaders,
    })
    const data = await res.json()
    return data?.data
  }
}

export async function queryUserTweets(userId: string, cursor?: string) {
  try {
    const api = await getTwitterApi()
    const tweetsResult = await api.getUserTweets(userId, {
      cursor,
      count: 20,
    })

    if (tweetsResult?.data) {
      return tweetsResult.data
    }

    throw new Error('No data returned from twitter-openapi-typescript')
  }
  catch (error: any) {
    logger.error(`获取用户推文失败，回退到原始方法: ${error.message}`)

    // 回退到原始的 fetch 方法
    const queryParams = {
      variables: {
        userId,
        cursor,
        count: 20,
        includePromotedContent: false,
        withQuickPromoteEligibilityTweetFields: false,
        withVoice: true,
        withV2Timeline: true,
      },
      features: {
        responsive_web_graphql_exclude_directive_enabled: true,
        verified_phone_label_enabled: false,
        creator_subscriptions_tweet_preview_api_enabled: true,
        responsive_web_graphql_timeline_navigation_enabled: true,
        responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
        c9s_tweet_anatomy_moderator_badge_enabled: true,
        tweetypie_unmention_optimization_enabled: true,
        responsive_web_edit_tweet_api_enabled: true,
        graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
        view_counts_everywhere_api_enabled: true,
        longform_notetweets_consumption_enabled: true,
        responsive_web_twitter_article_tweet_consumption_enabled: true,
        tweet_awards_web_tipping_enabled: false,
        freedom_of_speech_not_reach_fetch_enabled: true,
        standardized_nudges_misinfo: true,
        tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
        rweb_video_timestamps_enabled: true,
        longform_notetweets_rich_text_read_enabled: true,
        longform_notetweets_inline_media_enabled: true,
        responsive_web_media_download_video_enabled: true,
        responsive_web_enhance_cards_enabled: false,
      },
    }
    if (cursor) {
      queryParams.variables.cursor = cursor
    }
    // 构建查询参数字符串
    const queryString = toQueryString(queryParams)
    const userApi = `https://twitter.com/i/api/graphql/5ICa5d9-AitXZrIA3H-4MQ/UserTweets?${queryString}`
    const res = await fetch(userApi, {
      method: 'GET',
      headers: userHeaders,
    })
    const data = await res.json()
    return data?.data
  }
}

export function fmtUserTweets(data: any) {
  const instructions = get(data, [
    'user',
    'result',
    'timeline_v2',
    'timeline',
    'instructions',
  ])
  // const pinObj = find(instructions, ['type', 'TimelinePinEntry'])?.entry
  const isFirstPage = !!find(instructions, ['type', 'TimelineClearCache'])
  const postsList = find(instructions, ['type', 'TimelineAddEntries'])?.entries
  // top 分页
  const topCursor = find(postsList, item => item?.content?.cursorType === 'Top')
  const topCursorId = get(topCursor, ['content', 'value'])
  // btm 分页
  const bottomCursor = find(postsList, item => item?.content?.cursorType === 'Bottom')
  const bottomCursorId = get(bottomCursor, ['content', 'value'])
  // user tweets
  const tweetsRes = filter(postsList, item => item.entryId.split('-')[0] === 'tweet')
  const tweets = map(tweetsRes, (item: any) => {
    return getTweetResult(item, item.entryId.split('-')[1], postsList)
  })

  // 不鞋带推文置顶消息
  // if (isFirstPage && pinObj) {
  //   const pin = get(pinObj, ['content', 'itemContent', 'tweet_results', 'result'])
  //   if (pin.legacy) {
  //     tweets.unshift(pin)
  //   }
  // }

  return {
    tweets,
    topCursorId,
    bottomCursorId,
    isFirstPage,
  }
}

export type UserTweetsType = ReturnType<typeof fmtUserTweets>
