import { logger } from '#root/logger.js'

// Twitter API 配置
export const TWITTER_CONFIG = {
  // 重试配置
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  
  // 请求超时
  timeout: 30000, // 30秒
  
  // Cookie 轮换配置
  cookieRotationEnabled: true,
  
  // 回退策略配置
  fallbackToPlaywright: true,
}

// 错误类型定义
export class TwitterApiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'TwitterApiError'
  }
}

// 重试工具函数
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = TWITTER_CONFIG.maxRetries,
  delay: number = TWITTER_CONFIG.retryDelay
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error: any) {
      lastError = error
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break
      }
      
      // 检查是否是可重试的错误
      if (!isRetryableError(error)) {
        throw error
      }
      
      logger.warn(`Twitter API 请求失败，第 ${attempt} 次重试，${delay}ms 后重试: ${error.message}`)
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }
  
  throw new TwitterApiError(
    `Twitter API 请求在 ${maxRetries} 次重试后仍然失败`,
    'MAX_RETRIES_EXCEEDED',
    undefined,
    lastError
  )
}

// 判断是否是可重试的错误
function isRetryableError(error: any): boolean {
  // 网络错误
  if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.code === 'ENOTFOUND') {
    return true
  }
  
  // HTTP 状态码错误
  if (error.statusCode) {
    // 5xx 服务器错误可重试
    if (error.statusCode >= 500 && error.statusCode < 600) {
      return true
    }
    
    // 429 限流错误可重试
    if (error.statusCode === 429) {
      return true
    }
  }
  
  // Twitter API 特定错误
  if (error.message?.includes('Rate limit exceeded')) {
    return true
  }
  
  if (error.message?.includes('Service Unavailable')) {
    return true
  }
  
  return false
}

// 日志记录工具
export function logTwitterApiCall(method: string, params: any, duration: number, success: boolean) {
  const logData = {
    method,
    params: JSON.stringify(params),
    duration: `${duration}ms`,
    success,
  }
  
  if (success) {
    logger.info(`Twitter API 调用成功: ${JSON.stringify(logData)}`)
  } else {
    logger.error(`Twitter API 调用失败: ${JSON.stringify(logData)}`)
  }
}
