import { getTweetDetail, queryUser, queryUserTweets } from './twitter.js'
import { logger } from '#root/logger.js'

// 测试推特详情获取
export async function testTweetDetail() {
  const testUrls = [
    'https://x.com/elonmusk/status/1234567890123456789', // 示例URL，需要替换为真实的
    'https://twitter.com/openai/status/1234567890123456789', // 示例URL，需要替换为真实的
  ]

  for (const url of testUrls) {
    try {
      logger.info(`测试获取推特详情: ${url}`)
      const result = await getTweetDetail(url)
      logger.info(`成功获取推特详情:`, {
        hasMedia: result.media?.length > 0,
        hasLegacy: !!result.legacy,
        hasUser: !!result.user,
        tombstone: !!result.tombstone,
      })
    } catch (error: any) {
      logger.error(`获取推特详情失败: ${url}`, error.message)
    }
  }
}

// 测试用户信息获取
export async function testQueryUser() {
  const testUsers = [
    'elonmusk',
    'openai',
    'twitter',
  ]

  for (const screenName of testUsers) {
    try {
      logger.info(`测试获取用户信息: ${screenName}`)
      const result = await queryUser(screenName)
      logger.info(`成功获取用户信息:`, {
        hasResult: !!result.user_result_by_screen_name,
        userId: result.user_result_by_screen_name?.result?.rest_id,
        screenName: result.user_result_by_screen_name?.result?.legacy?.screen_name,
      })
    } catch (error: any) {
      logger.error(`获取用户信息失败: ${screenName}`, error.message)
    }
  }
}

// 测试用户推文获取
export async function testQueryUserTweets() {
  const testUserId = '44196397' // Elon Musk 的用户ID，需要替换为真实的

  try {
    logger.info(`测试获取用户推文: ${testUserId}`)
    const result = await queryUserTweets(testUserId)
    logger.info(`成功获取用户推文:`, {
      hasData: !!result,
      // 可以添加更多的结构检查
    })
  } catch (error: any) {
    logger.error(`获取用户推文失败: ${testUserId}`, error.message)
  }
}

// 运行所有测试
export async function runAllTests() {
  logger.info('开始运行 Twitter API 测试...')
  
  try {
    await testQueryUser()
    await testTweetDetail()
    await testQueryUserTweets()
    logger.info('所有测试完成')
  } catch (error: any) {
    logger.error('测试过程中发生错误:', error.message)
  }
}

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error)
}
