/** 推特相关 */

import type { TweetDetail } from '@prisma/client'
import { pick } from 'lodash-es'
import prisma from '#root/db/index.js'

/**
 * 获取推文详情
 * @param tweetId
 */

export async function getTweetDetailByTweetId(tweetId: string) {
  const tweetDetail = await prisma.tweetDetail.findUnique({
    where: { tweetId },
  }) as Omit<TweetDetail, 'mediaGroups'> & { mediaGroups: any[] }
  return tweetDetail
}

export async function createTweetDetail(payload: Pick<TweetDetail, 'tweetId' | 'url' | 'sourceTweet'>) {
  const data = pick(payload, ['tweetId', 'url', 'sourceTweet'])
  const tweetDetail = await prisma.tweetDetail.upsert({
    where: { tweetId: data.tweetId },
    create: {
      ...data,
      sourceTweet: data.sourceTweet as any,
    },
    update: {
      sourceTweet: data.sourceTweet as any,
    },
  }) as Omit<TweetDetail, 'mediaGroups'> & { mediaGroups: any[] }
  return tweetDetail
}
// 批量创建推文详情
export async function createManyTweetDetail(tweets: Pick<TweetDetail, 'tweetId' | 'url' | 'sourceTweet'>[]) {
  return await prisma.tweetDetail.createMany({
    data: tweets.map(tweet => ({
      tweetId: tweet.tweetId,
      url: tweet.url,
      sourceTweet: tweet.sourceTweet as any,
    })),
    skipDuplicates: true,
  })
}

export async function updateTweetDetailByTweetId(tweetId: string, payload: Partial<TweetDetail>) {
  const data = pick(payload, ['tweetId', 'url', 'sourceTweet', 'count', 'mediaGroups'])
  const tweetDetail = await prisma.tweetDetail.update({
    where: { tweetId },
    data: {
      ...data,
      sourceTweet: data.sourceTweet as any,
      mediaGroups: data.mediaGroups as any,
    },
  })
  return tweetDetail
}

// 创建用户推文
export async function createUserTweets(userId: string, cursor: string | undefined, data: any) {
  return await prisma.userTweets.upsert({
    where: {
      userId_cursor: {
        userId,
        cursor: cursor || 'initial',
      },
    },
    create: {
      userId,
      cursor: cursor || 'initial',
      result: data,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24),
    },
    update: {},
  })
}

export async function getUserTweets(userId: string, cursor?: string | undefined) {
  const userTweets = await prisma.userTweets.findUnique({
    where: {
      userId_cursor: {
        userId,
        cursor: cursor || 'initial',
      },
    },
  })
  return userTweets
}
export async function getUserTweetsById(userTweetsId: string) {
  const userTweets = await prisma.userTweets.findUnique({
    where: {
      id: userTweetsId,
    },
  })

  return userTweets
}

/**
 * 删除过期的 UserTweets（ 24 小时前的数据）
 */
export async function deleteExpiredUserTweets() {
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

  return await prisma.userTweets.deleteMany({
    where: {
      createdAt: {
        lt: twentyFourHoursAgo,
      },
    },
  })
}
